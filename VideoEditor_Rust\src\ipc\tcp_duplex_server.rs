// ******************************************************
// 文件名: tcp_duplex_server.rs  
// 功能描述: 标准TCP全双工本地通信服务器
//
// 设计理念：
//   1. 标准TCP实践 - 使用127.0.0.1本地回环
//   2. 真正全双工 - socket.into_split()分离读写
//   3. 简洁可靠 - 一个连接处理所有双向通信
//   4. 完全集成 - 命令处理直接集成到连接中
// ******************************************************

use tokio::net::{TcpListener, TcpStream};
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
use log::{info, warn, error};
use tokio::sync::mpsc::{self, Receiver, Sender};
use std::sync::{Arc, Mutex};
use std::sync::OnceLock;

use super::{Message, UniversalMessage, Command};

/// 默认本地TCP地址 - 使用程序特有端口避免冲突
/// 端口选择：E剪Pro -> ECUT -> E(69)+C(67)+U(85)+T(84) = 305 -> 30505
const DEFAULT_LOCAL_ADDRESS: &str = "127.0.0.1:30505";

/// 全局命令处理器存储
static COMMAND_HANDLER: OnceLock<Arc<Mutex<Option<Box<dyn Fn(Command) + Send + 'static>>>>> = OnceLock::new();

/// TCP全双工服务器 - 标准本地通信实现
pub struct TcpDuplexServer {
    /// 消息发送通道
    message_sender: Sender<Message>,
}

impl TcpDuplexServer {
    /// 创建新的TCP全双工服务器
    pub fn new<F>(address: Option<&str>, disconnect_handler: F) -> Self
    where
        F: Fn() + Send + 'static
    {
        let (message_sender, message_receiver) = mpsc::channel::<Message>(100);
        let address = address.unwrap_or(DEFAULT_LOCAL_ADDRESS).to_string();

        // 启动TCP服务器，等待连接
        let server_address = address.clone();
        tokio::spawn(async move {
            Self::run_tcp_server(server_address, message_receiver, disconnect_handler).await;
        });

        Self {
            message_sender,
        }
    }

    /// 发送视频处理消息
    pub async fn send_video_processing(&self, message: UniversalMessage) {
        let _ = self.message_sender.send(Message::VideoProcessing(message)).await;
    }

    /// 发送预览结果消息
    pub async fn send_preview(&self, message: UniversalMessage) {
        let _ = self.message_sender.send(Message::Preview(message)).await;
    }

    /// 发送GPU检查结果消息
    pub async fn send_gpu_check(&self, message: UniversalMessage) {
        let _ = self.message_sender.send(Message::GpuCheck(message)).await;
    }

    /// 发送视频分析结果消息
    pub async fn send_video_analysis(&self, message: UniversalMessage) {
        let _ = self.message_sender.send(Message::VideoAnalysis(message)).await;
    }

    /// 启动命令监听器 - 兼容接口
    pub fn start_command_listener<F>(&self, command_handler: F)
    where
        F: Fn(Command) + Send + 'static + Clone
    {
        // 保存命令处理器到全局存储
        let handler = COMMAND_HANDLER.get_or_init(|| {
            Arc::new(Mutex::new(None))
        });

        if let Ok(mut guard) = handler.lock() {
            *guard = Some(Box::new(command_handler));
        }

        info!("🎧 命令处理器已注册，等待TCP连接建立");
    }

    /// TCP服务器主循环
    async fn run_tcp_server<F>(
        address: String,
        message_receiver: Receiver<Message>,
        disconnect_handler: F,
    )
    where
        F: Fn() + Send + 'static
    {
        info!("🚀 启动TCP本地服务器: {}", address);

        // 创建TCP监听器
        let listener = match TcpListener::bind(&address).await {
            Ok(l) => {
                info!("✅ TCP监听器已启动: {}", address);
                l
            }
            Err(e) => {
                error!("❌ TCP监听器启动失败: {} - {}", address, e);
                disconnect_handler();
                return;
            }
        };

        // 等待前端连接
        let (socket, client_addr) = match listener.accept().await {
            Ok((s, addr)) => {
                info!("🔗 前端已连接: {} - 开始全双工通信", addr);
                (s, addr)
            }
            Err(e) => {
                error!("❌ 接受前端连接失败: {}", e);
                disconnect_handler();
                return;
            }
        };

        // 处理TCP全双工连接
        Self::handle_tcp_duplex_connection(socket, message_receiver).await;

        info!("🔌 前端连接已断开: {}", client_addr);
        disconnect_handler();
    }

    /// 处理TCP全双工连接 - 标准实现
    async fn handle_tcp_duplex_connection(
        socket: TcpStream,
        message_receiver: Receiver<Message>,
    ) {
        let client_addr = socket.peer_addr().unwrap();
        info!("🔗 开始处理TCP全双工连接: {}", client_addr);

        // 🔥 关键：分离读写端 - TCP全双工标准做法
        let (reader, writer) = socket.into_split();

        // 启动写入任务（消息推送）
        let write_handle = tokio::spawn(async move {
            Self::handle_message_pushing(writer, message_receiver).await;
        });

        // 启动读取任务（命令接收）
        let read_handle = tokio::spawn(async move {
            Self::handle_command_receiving(reader).await;
        });

        // 等待任一任务结束
        tokio::select! {
            _ = write_handle => info!("📝 TCP写入任务结束: {}", client_addr),
            _ = read_handle => info!("📖 TCP读取任务结束: {}", client_addr),
        }

        info!("🔌 TCP全双工连接处理完成: {}", client_addr);
    }

    /// 消息推送处理
    async fn handle_message_pushing(
        mut writer: tokio::net::tcp::OwnedWriteHalf,
        mut message_receiver: Receiver<Message>,
    ) {
        info!("📡 开始TCP消息推送循环");

        while let Some(message) = message_receiver.recv().await {
            let json = match serde_json::to_string(&message) {
                Ok(j) => j,
                Err(e) => {
                    error!("❌ 消息序列化失败: {}", e);
                    continue;
                }
            };

            let data = format!("{}\n", json);
            if let Err(e) = writer.write_all(data.as_bytes()).await {
                error!("❌ 消息发送失败: {} - 连接断开", e);
                break;
            }
        }

        info!("📡 TCP消息推送循环结束");
    }

    /// 命令接收处理
    async fn handle_command_receiving(reader: tokio::net::tcp::OwnedReadHalf) {
        let mut buf_reader = BufReader::new(reader);
        let mut line = String::new();

        info!("🎧 开始TCP命令接收循环");

        while let Ok(bytes_read) = buf_reader.read_line(&mut line).await {
            if bytes_read == 0 {
                info!("🔌 前端命令连接已关闭");
                break;
            }

            match serde_json::from_str::<Command>(line.trim()) {
                Ok(cmd) => {
                    let cmd_name = match &cmd {
                        Command::ProcessVideos(_) => "ProcessVideos",
                        Command::CancelAllTasks => "CancelAllTasks",
                        Command::AnalyzeVideo(_) => "AnalyzeVideo",
                        Command::PreviewEffect { .. } => "PreviewEffect",
                        Command::CheckGpuAcceleration(_) => "CheckGpuAcceleration",
                    };
                    info!("📨 收到TCP命令: {}", cmd_name);

                    // 调用命令处理器
                    if let Some(handler) = COMMAND_HANDLER.get() {
                        if let Ok(guard) = handler.lock() {
                            if let Some(ref handler_fn) = *guard {
                                handler_fn(cmd);
                            }
                        }
                    }
                }
                Err(e) => {
                    warn!("❌ 命令解析失败: {}", e);
                }
            }

            line.clear();
        }

        info!("🎧 TCP命令接收循环结束");
    }

}
