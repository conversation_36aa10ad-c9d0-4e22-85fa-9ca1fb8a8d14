// ******************************************************
// 文件名: TcpVideoService.cs
// 功能描述: TCP Socket视频服务 - 简洁但生产级别的.NET通信
// 主要职责:
//   1. TCP Socket双向通信 - 使用.NET内置TcpClient
//   2. 事件驱动架构 - 实时接收后端推送
//   3. 生产级稳定性 - 自动重连、异常处理
//   4. 完全兼容原有接口 - 无缝替换PipeVideoService
// ******************************************************

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using ECutPro.Models;
using ECutPro.VideoEffects;

namespace ECutPro.Services
{
    /// <summary>
    /// TCP Socket视频服务 - 简洁但生产级别
    /// </summary>
    public sealed class TcpVideoService : IDisposable
    {
        #region 事件定义

        /// <summary>
        /// 视频处理消息接收事件（进度、状态等）
        /// </summary>
        public event Action<UniversalMessage>? VideoProcessingReceived;

        /// <summary>
        /// 预览结果接收事件
        /// </summary>
        public event Action<UniversalMessage>? PreviewReceived;

        /// <summary>
        /// 视频分析结果接收事件
        /// </summary>
        public event Action<UniversalMessage>? AnalysisReceived;

        /// <summary>
        /// GPU检查结果接收事件
        /// </summary>
        public event Action<UniversalMessage>? GpuCheckReceived;

        /// <summary>
        /// 连接状态变更事件
        /// </summary>
        public event Action<bool>? ConnectionStateChanged;

        #endregion

        #region 私有字段

        private TcpClient? _tcpClient;
        private StreamReader? _reader;
        private StreamWriter? _writer;
        private CancellationTokenSource? _cancellation;
        private bool _disposed;

        private const string ServerAddress = "127.0.0.1";
        private const int ServerPort = 30505;

        #endregion

        #region 连接管理

        /// <summary>
        /// 连接到后端 - 3次重试，生产级稳定性
        /// </summary>
        public async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(TcpVideoService));

            for (int attempt = 1; attempt <= 3; attempt++)
            {
                try
                {
                    _tcpClient = new TcpClient();
                    await _tcpClient.ConnectAsync(ServerAddress, ServerPort, cancellationToken);

                    var stream = _tcpClient.GetStream();
                    _reader = new StreamReader(stream);
                    _writer = new StreamWriter(stream) { AutoFlush = true };

                    _cancellation = new CancellationTokenSource();
                    _ = Task.Run(() => ReceiveLoop(_cancellation.Token), cancellationToken);

                    ConnectionStateChanged?.Invoke(true);
                    return;
                }
                catch when (attempt < 3)
                {
                    Cleanup();
                    await Task.Delay(500, cancellationToken);
                }
            }

            throw new InvalidOperationException("连接失败，请重启后端服务");
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            Cleanup();
            ConnectionStateChanged?.Invoke(false);
            await Task.CompletedTask;
        }

        /// <summary>
        /// 清理资源 - 简洁但安全
        /// </summary>
        private void Cleanup()
        {
            _cancellation?.Cancel();
            _writer?.Dispose();
            _reader?.Dispose();
            _tcpClient?.Close();
            _cancellation?.Dispose();

            _writer = null;
            _reader = null;
            _tcpClient = null;
            _cancellation = null;
        }

        #endregion

        #region 命令发送方法

        /// <summary>
        /// 处理视频批量任务
        /// </summary>
        public async Task ProcessVideosAsync(List<string> inputPaths, string outputDir, List<VideoEffect> effects, ProcessingOptions options, CancellationToken cancellationToken = default)
        {
            await SendCommandAsync(new
            {
                type = "ProcessVideos",
                data = new ProcessVideosCommand
                {
                    InputPaths = inputPaths,
                    OutputDir = outputDir,
                    Effects = effects.Where(e => e.IsEnabled).Select(e => e.CreateEffectParam()).ToList(),
                    Options = options
                }
            }, cancellationToken);
        }

        /// <summary>
        /// 取消所有任务
        /// </summary>
        public async Task CancelAllTasksAsync(CancellationToken cancellationToken = default)
        {
            await SendCommandAsync(new { type = "CancelAllTasks" }, cancellationToken);
        }

        /// <summary>
        /// 分析视频文件
        /// </summary>
        public async Task AnalyzeVideoAsync(string videoPath, CancellationToken cancellationToken = default)
        {
            await SendCommandAsync(new { type = "AnalyzeVideo", data = videoPath }, cancellationToken);
        }

        /// <summary>
        /// 预览视频效果
        /// </summary>
        public async Task PreviewEffectAsync(string videoPath, VideoEffect effect, CancellationToken cancellationToken = default)
        {
            await SendCommandAsync(new
            {
                type = "PreviewEffect",
                data = new PreviewEffectCommand { VideoPath = videoPath, Effect = effect.CreateEffectParam() }
            }, cancellationToken);
        }

        /// <summary>
        /// 检查GPU加速可用性
        /// </summary>
        public async Task CheckGpuAccelerationAsync(string gpuType, CancellationToken cancellationToken = default)
        {
            await SendCommandAsync(new { type = "CheckGpuAcceleration", data = gpuType }, cancellationToken);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 发送命令 - 简洁纯净，异常向上传播
        /// </summary>
        private async Task SendCommandAsync(object command, CancellationToken cancellationToken = default)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(TcpVideoService));
            if (_writer == null) throw new InvalidOperationException("未连接到服务器");

            var json = JsonSerializer.Serialize(command);
            await _writer.WriteLineAsync(json.AsMemory(), cancellationToken);
        }

        /// <summary>
        /// 消息接收循环 - 生产级稳定性
        /// </summary>
        private async Task ReceiveLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && _reader != null)
                {
                    var line = await _reader.ReadLineAsync(cancellationToken);
                    if (line == null) break;

                    ProcessMessage(line);
                }
            }
            catch (OperationCanceledException) { }
            catch
            {
                ConnectionStateChanged?.Invoke(false);
            }
        }

        /// <summary>
        /// 处理消息 - 简洁但完整
        /// </summary>
        private void ProcessMessage(string json)
        {
            try
            {
                // 添加调试信息
                Debug.WriteLine($"📨 前端收到消息: {json}");

                using var doc = JsonDocument.Parse(json);
                var root = doc.RootElement;

                if (!root.TryGetProperty("type", out var type) ||
                    !root.TryGetProperty("data", out var data))
                {
                    Debug.WriteLine("❌ 消息格式错误：缺少type或data字段");
                    return;
                }

                Debug.WriteLine($"📋 消息类型: {type.GetString()}");
                Debug.WriteLine($"📋 消息数据: {data.GetRawText()}");

                var message = JsonSerializer.Deserialize<UniversalMessage>(data.GetRawText());
                if (message == null)
                {
                    Debug.WriteLine("❌ 无法反序列化UniversalMessage");
                    return;
                }

                Debug.WriteLine($"✅ 成功解析消息，success={message.Success}");

                switch (type.GetString())
                {
                    case "VideoProcessing":
                        Debug.WriteLine("🎬 触发VideoProcessingReceived事件");
                        VideoProcessingReceived?.Invoke(message);
                        break;
                    case "Preview":
                        Debug.WriteLine("👁️ 触发PreviewReceived事件");
                        PreviewReceived?.Invoke(message);
                        break;
                    case "VideoAnalysis":
                        Debug.WriteLine("🔍 触发AnalysisReceived事件");
                        AnalysisReceived?.Invoke(message);
                        break;
                    case "GpuCheck":
                        Debug.WriteLine("🚀 触发GpuCheckReceived事件");
                        GpuCheckReceived?.Invoke(message);
                        break;
                    default:
                        Debug.WriteLine($"❓ 未知消息类型: {type.GetString()}");
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ 消息处理异常: {ex.Message}");
                Debug.WriteLine($"❌ 原始消息: {json}");
            }
        }



        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源 - 简洁但安全
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
            Cleanup();
        }

        #endregion
    }
}
